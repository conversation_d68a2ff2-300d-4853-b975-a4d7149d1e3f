package com.nacos.utils;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Disabled;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 阿里云机器翻译工具类测试
 * 
 * <AUTHOR>
 * @since 2025-08-05
 */
@SpringBootTest
public class AliTranslateUtilTest {

    /**
     * 测试单文本翻译 - 中文到英文
     * 注意：此测试需要真实的阿里云API密钥，在CI环境中应该禁用
     */
    @Test
    @Disabled("需要真实的阿里云API密钥，仅在本地测试时启用")
    public void testTranslateTextChineseToEnglish() {
        // 测试数据
        String sourceText = "你好，世界！";
        String sourceLanguage = "zh";
        String targetLanguage = "en";
        String scene = "general";

        // 执行翻译
        AliTranslateUtil.TranslationResult result = AliTranslateUtil.translateText(
                sourceText, sourceLanguage, targetLanguage, scene);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getTranslatedText());
        assertFalse(result.getTranslatedText().trim().isEmpty());
        assertTrue(result.getWordCount() > 0);
        
        System.out.println("原文: " + sourceText);
        System.out.println("译文: " + result.getTranslatedText());
        System.out.println("词数: " + result.getWordCount());
        System.out.println("检测语言: " + result.getDetectedLanguage());
    }

    /**
     * 测试单文本翻译 - 英文到中文
     */
    @Test
    @Disabled("需要真实的阿里云API密钥，仅在本地测试时启用")
    public void testTranslateTextEnglishToChinese() {
        // 测试数据
        String sourceText = "Hello, world!";
        String sourceLanguage = "en";
        String targetLanguage = "zh";
        String scene = "general";

        // 执行翻译
        AliTranslateUtil.TranslationResult result = AliTranslateUtil.translateText(
                sourceText, sourceLanguage, targetLanguage, scene);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getTranslatedText());
        assertFalse(result.getTranslatedText().trim().isEmpty());
        
        System.out.println("原文: " + sourceText);
        System.out.println("译文: " + result.getTranslatedText());
    }

    /**
     * 测试相同语言翻译（应该直接返回原文）
     */
    @Test
    public void testTranslateTextSameLanguage() {
        // 测试数据
        String sourceText = "Hello, world!";
        String sourceLanguage = "en";
        String targetLanguage = "en";
        String scene = "general";

        // 执行翻译
        AliTranslateUtil.TranslationResult result = AliTranslateUtil.translateText(
                sourceText, sourceLanguage, targetLanguage, scene);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(sourceText, result.getTranslatedText());
    }

    /**
     * 测试空文本翻译
     */
    @Test
    public void testTranslateTextEmpty() {
        // 测试数据
        String sourceText = "";
        String sourceLanguage = "zh";
        String targetLanguage = "en";
        String scene = "general";

        // 执行翻译
        AliTranslateUtil.TranslationResult result = AliTranslateUtil.translateText(
                sourceText, sourceLanguage, targetLanguage, scene);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("源文本不能为空", result.getErrorMessage());
    }

    /**
     * 测试空语言代码
     */
    @Test
    public void testTranslateTextEmptyLanguage() {
        // 测试数据
        String sourceText = "Hello";
        String sourceLanguage = "";
        String targetLanguage = "zh";
        String scene = "general";

        // 执行翻译
        AliTranslateUtil.TranslationResult result = AliTranslateUtil.translateText(
                sourceText, sourceLanguage, targetLanguage, scene);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("语言代码不能为空", result.getErrorMessage());
    }

    /**
     * 测试超长文本翻译
     */
    @Test
    public void testTranslateTextTooLong() {
        // 构造超长文本（超过5000字符）
        StringBuilder longText = new StringBuilder();
        for (int i = 0; i < 5001; i++) {
            longText.append("a");
        }

        // 执行翻译
        AliTranslateUtil.TranslationResult result = AliTranslateUtil.translateText(
                longText.toString(), "en", "zh", "general");

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertTrue(result.getErrorMessage().contains("文本长度超过"));
    }

    /**
     * 测试批量翻译
     */
    @Test
    @Disabled("需要真实的阿里云API密钥，仅在本地测试时启用")
    public void testTranslateBatch() {
        // 测试数据
        List<String> textList = Arrays.asList(
                "你好",
                "再见",
                "谢谢",
                "不客气"
        );
        String sourceLanguage = "zh";
        String targetLanguage = "en";
        String scene = "general";

        // 执行批量翻译
        AliTranslateUtil.BatchTranslationResult result = AliTranslateUtil.translateBatch(
                textList, sourceLanguage, targetLanguage, scene);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(textList.size(), result.getTranslatedTexts().size());
        assertEquals(textList.size(), result.getSuccessCount());

        // 打印结果
        for (int i = 0; i < textList.size(); i++) {
            System.out.println("原文: " + textList.get(i) + " -> 译文: " + result.getTranslatedTexts().get(i));
        }
    }

    /**
     * 测试批量翻译 - 空列表
     */
    @Test
    public void testTranslateBatchEmpty() {
        // 执行批量翻译
        AliTranslateUtil.BatchTranslationResult result = AliTranslateUtil.translateBatch(
                null, "zh", "en", "general");

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("文本列表不能为空", result.getErrorMessage());
    }

    /**
     * 测试字幕翻译场景
     */
    @Test
    @Disabled("需要真实的阿里云API密钥，仅在本地测试时启用")
    public void testSubtitleTranslation() {
        // 模拟字幕内容
        String subtitleContent = "WEBVTT\n\n" +
                "00:00:01.000 --> 00:00:05.000\n" +
                "大家好，欢迎观看这个视频。\n\n" +
                "00:00:05.000 --> 00:00:10.000\n" +
                "今天我们将学习如何使用机器翻译。\n\n" +
                "00:00:10.000 --> 00:00:15.000\n" +
                "希望这个教程对大家有帮助。";

        // 执行翻译
        AliTranslateUtil.TranslationResult result = AliTranslateUtil.translateText(
                subtitleContent, "zh", "en", "general");

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getTranslatedText());
        
        System.out.println("原字幕:");
        System.out.println(subtitleContent);
        System.out.println("\n翻译后字幕:");
        System.out.println(result.getTranslatedText());
    }
}
