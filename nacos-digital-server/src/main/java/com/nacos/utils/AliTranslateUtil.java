package com.nacos.utils;

import com.alibaba.fastjson2.JSONObject;
import com.aliyun.alimt20181012.Client;
import com.aliyun.alimt20181012.models.TranslateGeneralRequest;
import com.aliyun.alimt20181012.models.TranslateGeneralResponse;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.nacos.config.OssClientConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 阿里云机器翻译工具类
 * 
 * <p>基于阿里云机器翻译服务（TranslateGeneral）实现文本翻译功能。
 * 支持多种语言翻译、批量翻译、异步翻译等功能。</p>
 * 
 * <h3>主要功能</h3>
 * <ul>
 *   <li>单文本翻译：支持各种语言对之间的翻译</li>
 *   <li>批量翻译：支持字幕等多段文本的批量翻译</li>
 *   <li>异步翻译：支持大量文本的异步并发翻译</li>
 *   <li>错误重试：内置重试机制，提高翻译成功率</li>
 *   <li>字符长度检查：自动处理超长文本分割</li>
 * </ul>
 * 
 * <h3>支持的语言</h3>
 * <ul>
 *   <li>中文（zh）↔ 英文（en）</li>
 *   <li>中文（zh）↔ 日文（ja）</li>
 *   <li>中文（zh）↔ 韩文（ko）</li>
 *   <li>中文（zh）↔ 法文（fr）</li>
 *   <li>中文（zh）↔ 德文（de）</li>
 *   <li>中文（zh）↔ 西班牙文（es）</li>
 *   <li>更多语言对请参考阿里云文档</li>
 * </ul>
 * 
 * <h3>使用示例</h3>
 * <pre>
 * // 单文本翻译
 * TranslationResult result = AliTranslateUtil.translateText("你好", "zh", "en", "general");
 * if (result.isSuccess()) {
 *     String translatedText = result.getTranslatedText();
 * }
 * 
 * // 批量翻译
 * List&lt;String&gt; texts = Arrays.asList("你好", "再见");
 * BatchTranslationResult batchResult = AliTranslateUtil.translateBatch(texts, "zh", "en", "general");
 * </pre>
 * 
 * <AUTHOR>
 * @since 2025-08-05
 * @version 1.0
 */
@Slf4j
public class AliTranslateUtil {

    private static final String MT_ENDPOINT = "mt.aliyuncs.com";
    private static final String REGION_ID = "cn-beijing";
    private static final int MAX_TEXT_LENGTH = 5000; // 阿里云单次翻译最大字符数
    private static final int MAX_RETRIES = 3; // 最大重试次数
    private static final int CONNECT_TIMEOUT = 10000; // 连接超时时间（毫秒）
    private static final int READ_TIMEOUT = 30000; // 读取超时时间（毫秒）

    // 单例客户端
    private static volatile Client client;

    /**
     * 获取阿里云翻译客户端（单例模式）
     */
    private static Client getClient() {
        if (client == null) {
            synchronized (AliTranslateUtil.class) {
                if (client == null) {
                    try {
                        Config config = new Config()
                                .setAccessKeyId(OssClientConfig.ACCESSKEYID)
                                .setAccessKeySecret(OssClientConfig.SECRETACCESSKEY);
                        
                        config.endpoint = MT_ENDPOINT;
                        config.regionId = REGION_ID;
                        config.setConnectTimeout(CONNECT_TIMEOUT);
                        config.setReadTimeout(READ_TIMEOUT);
                        
                        client = new Client(config);
                        log.info("阿里云翻译客户端初始化成功");
                    } catch (Exception e) {
                        log.error("初始化阿里云翻译客户端失败", e);
                        throw new RuntimeException("初始化阿里云翻译客户端失败", e);
                    }
                }
            }
        }
        return client;
    }

    /**
     * 翻译单个文本
     * 
     * @param sourceText 源文本
     * @param sourceLanguage 源语言代码（如：zh、en、ja等）
     * @param targetLanguage 目标语言代码（如：zh、en、ja等）
     * @param scene 翻译场景（general：通用翻译）
     * @return 翻译结果
     */
    public static TranslationResult translateText(String sourceText, String sourceLanguage, 
                                                String targetLanguage, String scene) {
        String methodName = "translateText";
        
        try {
            log.info("[{}] 开始翻译: {}→{}, textLength={}", 
                    methodName, sourceLanguage, targetLanguage, 
                    sourceText != null ? sourceText.length() : 0);
            
            // 参数验证
            if (!StringUtils.hasText(sourceText)) {
                log.warn("[{}] 源文本为空", methodName);
                return TranslationResult.failure("源文本不能为空");
            }

            if (!StringUtils.hasText(sourceLanguage) || !StringUtils.hasText(targetLanguage)) {
                log.warn("[{}] 语言代码不能为空: source={}, target={}",
                        methodName, sourceLanguage, targetLanguage);
                return TranslationResult.failure("语言代码不能为空");
            }

            // 检查文本长度（此时sourceText已经通过hasText验证，不会为null）
            if (sourceText.length() > MAX_TEXT_LENGTH) {
                log.warn("[{}] 文本长度超过限制: {} > {}",
                        methodName, sourceText.length(), MAX_TEXT_LENGTH);
                return TranslationResult.failure("文本长度超过" + MAX_TEXT_LENGTH + "字符限制");
            }
            
            // 如果源语言和目标语言相同，直接返回原文
            if (sourceLanguage.equalsIgnoreCase(targetLanguage)) {
                log.info("[{}] 源语言和目标语言相同，返回原文", methodName);
                return TranslationResult.success(sourceText, sourceText.length(), sourceLanguage);
            }
            
            // 执行翻译（带重试机制）
            for (int attempt = 1; attempt <= MAX_RETRIES; attempt++) {
                try {
                    TranslationResult result = doTranslate(sourceText, sourceLanguage, targetLanguage, scene);
                    if (result.isSuccess()) {
                        log.info("[{}] 翻译成功: attempt={}, originalLength={}, translatedLength={}", 
                                methodName, attempt, sourceText.length(), result.getTranslatedText().length());
                        return result;
                    } else if (attempt == MAX_RETRIES) {
                        return result; // 最后一次尝试失败，返回错误结果
                    }
                } catch (Exception e) {
                    log.warn("[{}] 翻译尝试失败: attempt={}/{}, error={}", 
                            methodName, attempt, MAX_RETRIES, e.getMessage());
                    if (attempt == MAX_RETRIES) {
                        return TranslationResult.failure("翻译失败: " + e.getMessage());
                    }
                    // 等待后重试
                    try {
                        Thread.sleep(1000 * attempt); // 递增等待时间
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        return TranslationResult.failure("翻译被中断");
                    }
                }
            }
            
            return TranslationResult.failure("翻译失败，已达到最大重试次数");
            
        } catch (Exception e) {
            log.error("[{}] 翻译异常", methodName, e);
            return TranslationResult.failure("翻译异常: " + e.getMessage());
        }
    }

    /**
     * 执行实际的翻译调用
     */
    private static TranslationResult doTranslate(String sourceText, String sourceLanguage, 
                                               String targetLanguage, String scene) throws Exception {
        Client translateClient = getClient();
        
        // 构建翻译请求
        TranslateGeneralRequest request = new TranslateGeneralRequest()
                .setSourceText(sourceText)
                .setSourceLanguage(sourceLanguage)
                .setTargetLanguage(targetLanguage)
                .setScene(scene != null ? scene : "general")
                .setFormatType("text");
        
        RuntimeOptions runtime = new RuntimeOptions();
        
        // 调用翻译API
        TranslateGeneralResponse response = translateClient.translateGeneralWithOptions(request, runtime);
        
        if (response == null || response.getBody() == null) {
            return TranslationResult.failure("翻译响应为空");
        }
        
        // 解析响应
        String responseBody = JSONObject.toJSONString(response.getBody());
        JSONObject responseJson = JSONObject.parseObject(responseBody);
        
        Integer code = responseJson.getInteger("code");
        String message = responseJson.getString("message");
        
        if (code == null || code != 200) {
            log.error("翻译API返回错误: code={}, message={}", code, message);
            return TranslationResult.failure("翻译失败: " + message);
        }
        
        JSONObject data = responseJson.getJSONObject("data");
        if (data == null) {
            return TranslationResult.failure("翻译响应数据为空");
        }
        
        String translatedText = data.getString("translated");
        String detectedLanguage = data.getString("detectedLanguage");
        Integer wordCount = data.getInteger("wordCount");
        
        if (!StringUtils.hasText(translatedText)) {
            return TranslationResult.failure("翻译结果为空");
        }
        
        return TranslationResult.success(translatedText, wordCount != null ? wordCount : 0, detectedLanguage);
    }

    /**
     * 批量翻译文本列表
     * 
     * @param textList 文本列表
     * @param sourceLanguage 源语言代码
     * @param targetLanguage 目标语言代码
     * @param scene 翻译场景
     * @return 批量翻译结果
     */
    public static BatchTranslationResult translateBatch(List<String> textList, String sourceLanguage, 
                                                      String targetLanguage, String scene) {
        String methodName = "translateBatch";
        
        try {
            log.info("[{}] 开始批量翻译: {}→{}, count={}", 
                    methodName, sourceLanguage, targetLanguage, 
                    textList != null ? textList.size() : 0);
            
            if (textList == null || textList.isEmpty()) {
                log.warn("[{}] 文本列表为空", methodName);
                return BatchTranslationResult.failure("文本列表不能为空");
            }
            
            List<String> translatedTexts = new ArrayList<>();
            List<String> errorMessages = new ArrayList<>();
            int successCount = 0;
            
            for (int i = 0; i < textList.size(); i++) {
                String text = textList.get(i);
                if (!StringUtils.hasText(text)) {
                    translatedTexts.add("");
                    errorMessages.add("文本为空");
                    continue;
                }
                
                TranslationResult result = translateText(text, sourceLanguage, targetLanguage, scene);
                if (result.isSuccess()) {
                    translatedTexts.add(result.getTranslatedText());
                    errorMessages.add(null);
                    successCount++;
                } else {
                    translatedTexts.add("");
                    errorMessages.add(result.getErrorMessage());
                }
            }
            
            log.info("[{}] 批量翻译完成: total={}, success={}, failed={}", 
                    methodName, textList.size(), successCount, textList.size() - successCount);
            
            return BatchTranslationResult.success(translatedTexts, errorMessages, successCount);
            
        } catch (Exception e) {
            log.error("[{}] 批量翻译异常", methodName, e);
            return BatchTranslationResult.failure("批量翻译异常: " + e.getMessage());
        }
    }

    /**
     * 翻译结果类
     */
    public static class TranslationResult {
        private boolean success;
        private String translatedText;
        private int wordCount;
        private String detectedLanguage;
        private String errorMessage;
        private long translationTime;

        private TranslationResult(boolean success, String translatedText, int wordCount, 
                                String detectedLanguage, String errorMessage) {
            this.success = success;
            this.translatedText = translatedText;
            this.wordCount = wordCount;
            this.detectedLanguage = detectedLanguage;
            this.errorMessage = errorMessage;
            this.translationTime = System.currentTimeMillis();
        }

        public static TranslationResult success(String translatedText, int wordCount, String detectedLanguage) {
            return new TranslationResult(true, translatedText, wordCount, detectedLanguage, null);
        }

        public static TranslationResult failure(String errorMessage) {
            return new TranslationResult(false, null, 0, null, errorMessage);
        }

        // Getters
        public boolean isSuccess() { return success; }
        public String getTranslatedText() { return translatedText; }
        public int getWordCount() { return wordCount; }
        public String getDetectedLanguage() { return detectedLanguage; }
        public String getErrorMessage() { return errorMessage; }
        public long getTranslationTime() { return translationTime; }
    }

    /**
     * 批量翻译结果类
     */
    public static class BatchTranslationResult {
        private boolean success;
        private List<String> translatedTexts;
        private List<String> errorMessages;
        private int successCount;
        private String errorMessage;

        private BatchTranslationResult(boolean success, List<String> translatedTexts, 
                                     List<String> errorMessages, int successCount, String errorMessage) {
            this.success = success;
            this.translatedTexts = translatedTexts;
            this.errorMessages = errorMessages;
            this.successCount = successCount;
            this.errorMessage = errorMessage;
        }

        public static BatchTranslationResult success(List<String> translatedTexts, 
                                                   List<String> errorMessages, int successCount) {
            return new BatchTranslationResult(true, translatedTexts, errorMessages, successCount, null);
        }

        public static BatchTranslationResult failure(String errorMessage) {
            return new BatchTranslationResult(false, null, null, 0, errorMessage);
        }

        // Getters
        public boolean isSuccess() { return success; }
        public List<String> getTranslatedTexts() { return translatedTexts; }
        public List<String> getErrorMessages() { return errorMessages; }
        public int getSuccessCount() { return successCount; }
        public String getErrorMessage() { return errorMessage; }
    }
}
